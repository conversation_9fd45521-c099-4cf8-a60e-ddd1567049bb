#!/usr/bin/env python3
"""
性能压力测试脚本
Performance Stress Test

进行高并发上传测试，验证系统在压力下的稳定性
"""

import asyncio
import aiohttp
import json
import time
import os
import sys
import statistics
from pathlib import Path
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor

# 测试配置
BASE_URL = "http://localhost:8000"
UPLOAD_ENDPOINT = f"{BASE_URL}/api/v1/documents/upload"
STATUS_ENDPOINT = f"{BASE_URL}/api/v1/documents/concurrent/status"
QUEUE_STATUS_ENDPOINT = f"{BASE_URL}/api/v1/documents/queue/status"
RESET_ENDPOINT = f"{BASE_URL}/api/v1/documents/concurrent/reset"
SYNC_ENDPOINT = f"{BASE_URL}/api/v1/documents/concurrent/sync"

# 测试文件配置
TEST_FILES_DIR = "src/docs/测试文档"


class StressTestRunner:
    """压力测试运行器"""
    
    def __init__(self):
        self.session = None
        self.test_results = []
        self.start_time = None
        self.end_time = None
        
    async def __aenter__(self):
        # 设置更大的连接池和超时
        connector = aiohttp.TCPConnector(
            limit=100,  # 总连接池大小
            limit_per_host=50,  # 每个主机的连接数
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        timeout = aiohttp.ClientTimeout(
            total=300,  # 总超时5分钟
            connect=30,  # 连接超时30秒
            sock_read=60  # 读取超时60秒
        )
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def get_test_file(self, file_id: int) -> str:
        """获取测试文件"""
        # 获取目录中的所有PDF文件
        all_files = []
        for filename in os.listdir(TEST_FILES_DIR):
            if filename.lower().endswith('.pdf'):
                filepath = os.path.join(TEST_FILES_DIR, filename)
                all_files.append(filepath)

        if not all_files:
            raise Exception(f"测试文件目录中没有PDF文件: {TEST_FILES_DIR}")

        # 循环使用文件
        file_index = file_id % len(all_files)
        return all_files[file_index]
    
    async def upload_single_file(self, file_path: str, worker_id: int) -> Dict[str, Any]:
        """上传单个文件"""
        filename = os.path.basename(file_path)

        try:
            with open(file_path, 'rb') as f:
                data = aiohttp.FormData()
                data.add_field('files', f, filename=filename, content_type='application/pdf')
                data.add_field('batch_name', f'压力测试_worker_{worker_id}_{int(time.time())}')

                start_time = time.time()
                async with self.session.post(UPLOAD_ENDPOINT, data=data) as response:
                    end_time = time.time()

                    result = {
                        "filename": filename,
                        "worker_id": worker_id,
                        "status_code": response.status,
                        "upload_time": end_time - start_time,
                        "success": response.status == 200,
                        "timestamp": start_time
                    }

                    if response.status == 200:
                        response_data = await response.json()
                        result["batch_id"] = response_data.get("batch_id")
                    else:
                        result["error"] = await response.text()

                    return result

        except Exception as e:
            return {
                "filename": filename,
                "worker_id": worker_id,
                "success": False,
                "error": str(e),
                "upload_time": 0,
                "timestamp": time.time()
            }
    
    async def worker_task(self, worker_id: int, files_per_worker: int, 
                         concurrent_connections: int, target_qps: int, duration: int) -> List[Dict[str, Any]]:
        """工作线程任务"""
        results = []
        
        # 确保测试文件目录存在
        os.makedirs(TEST_FILES_DIR, exist_ok=True)
        
        for i in range(files_per_worker):
            # 获取测试文件
            file_path = self.get_test_file(worker_id * files_per_worker + i + 1)

            # 上传文件
            result = await self.upload_single_file(file_path, worker_id)
            results.append(result)

            # 控制QPS
            if target_qps > 0:
                await asyncio.sleep(1.0 / target_qps)
        
        return results
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            concurrent_status = await self.session.get(STATUS_ENDPOINT)
            queue_status = await self.session.get(QUEUE_STATUS_ENDPOINT)
            
            result = {}
            
            if concurrent_status.status == 200:
                result["concurrent"] = await concurrent_status.json()
            
            if queue_status.status == 200:
                result["queue"] = await queue_status.json()
            
            return result
            
        except Exception as e:
            return {"error": str(e)}
    
    async def reset_system_state(self) -> bool:
        """重置系统状态"""
        try:
            async with self.session.post(RESET_ENDPOINT) as response:
                return response.status == 200
        except:
            return False
    
    async def run_stress_test(self, concurrent_connections: int = 10, 
                             files_per_worker: int = 5, target_qps: int = 2, 
                             duration_minutes: int = 2) -> Dict[str, Any]:
        """运行压力测试"""
        print(f"\n🚀 开始压力测试")
        print(f"  并发连接数: {concurrent_connections}")
        print(f"  每个工作线程文件数: {files_per_worker}")
        print(f"  目标QPS: {target_qps}")
        print(f"  预计持续时间: {duration_minutes} 分钟")
        print(f"  总文件数: {concurrent_connections * files_per_worker}")
        
        # 重置系统状态
        print("\n🔄 重置系统状态...")
        reset_success = await self.reset_system_state()
        if reset_success:
            print("  ✅ 系统状态已重置")
        else:
            print("  ⚠️  系统状态重置失败，继续测试")
        
        # 获取初始状态
        print("\n📊 获取初始状态...")
        initial_status = await self.get_system_status()
        
        # 启动压力测试
        print(f"\n⚡ 启动 {concurrent_connections} 个并发工作线程...")
        self.start_time = time.time()
        
        # 创建并发任务
        tasks = []
        for worker_id in range(concurrent_connections):
            task = asyncio.create_task(
                self.worker_task(worker_id, files_per_worker, concurrent_connections, 
                               target_qps, duration_minutes)
            )
            tasks.append(task)
        
        # 等待所有任务完成
        all_results = await asyncio.gather(*tasks, return_exceptions=True)
        self.end_time = time.time()
        
        # 处理结果
        successful_results = []
        failed_tasks = 0
        
        for i, result in enumerate(all_results):
            if isinstance(result, Exception):
                print(f"  ❌ 工作线程 {i} 异常: {result}")
                failed_tasks += 1
            else:
                successful_results.extend(result)
        
        # 等待系统处理
        print(f"\n⏳ 等待 30 秒让系统处理任务...")
        await asyncio.sleep(30)
        
        # 获取最终状态
        print("\n📊 获取最终状态...")
        final_status = await self.get_system_status()
        
        # 生成测试报告
        test_summary = self.generate_stress_report(
            successful_results, failed_tasks, concurrent_connections, 
            files_per_worker, target_qps, duration_minutes,
            initial_status, final_status
        )
        
        # 清理测试目录
        self.cleanup_test_directory()
        
        return test_summary
    
    def generate_stress_report(self, results: List[Dict], failed_tasks: int,
                              concurrent_connections: int, files_per_worker: int,
                              target_qps: int, duration_minutes: int,
                              initial_status: Dict, final_status: Dict) -> Dict[str, Any]:
        """生成压力测试报告"""
        print("\n" + "="*70)
        print("📋 压力测试报告")
        print("="*70)
        
        # 基本统计
        total_files = len(results)
        successful_uploads = sum(1 for r in results if r["success"])
        failed_uploads = total_files - successful_uploads
        success_rate = (successful_uploads / total_files * 100) if total_files > 0 else 0
        
        test_duration = self.end_time - self.start_time if self.start_time and self.end_time else 0
        actual_qps = total_files / test_duration if test_duration > 0 else 0
        
        print(f"\n📊 基本统计:")
        print(f"  总文件数: {total_files}")
        print(f"  成功上传: {successful_uploads}")
        print(f"  失败上传: {failed_uploads}")
        print(f"  失败任务: {failed_tasks}")
        print(f"  成功率: {success_rate:.1f}%")
        print(f"  测试持续时间: {test_duration:.2f}s")
        print(f"  实际QPS: {actual_qps:.2f}")
        print(f"  目标QPS: {target_qps}")
        
        # 性能统计
        if results:
            upload_times = [r["upload_time"] for r in results if r["success"]]
            if upload_times:
                print(f"\n⚡ 性能统计:")
                print(f"  平均响应时间: {statistics.mean(upload_times):.2f}s")
                print(f"  最快响应时间: {min(upload_times):.2f}s")
                print(f"  最慢响应时间: {max(upload_times):.2f}s")
                print(f"  响应时间中位数: {statistics.median(upload_times):.2f}s")
                if len(upload_times) > 1:
                    print(f"  响应时间标准差: {statistics.stdev(upload_times):.2f}s")
        
        # 错误分析
        if failed_uploads > 0:
            print(f"\n❌ 错误分析:")
            error_types = {}
            status_codes = {}
            
            for result in results:
                if not result["success"]:
                    error = result.get("error", "Unknown error")
                    error_types[error] = error_types.get(error, 0) + 1
                    
                    status_code = result.get("status_code", "N/A")
                    status_codes[status_code] = status_codes.get(status_code, 0) + 1
            
            print("  错误类型分布:")
            for error, count in error_types.items():
                print(f"    {error}: {count}")
            
            print("  状态码分布:")
            for code, count in status_codes.items():
                print(f"    {code}: {count}")
        
        # 系统状态对比
        print(f"\n🖥️  系统状态对比:")
        
        def extract_concurrent_info(status):
            return status.get("concurrent", {}).get("concurrent_info", {}) if status else {}
        
        def extract_queue_info(status):
            return status.get("queue", {}).get("queue_stats", {}) if status else {}
        
        initial_concurrent = extract_concurrent_info(initial_status)
        final_concurrent = extract_concurrent_info(final_status)
        
        initial_queue = extract_queue_info(initial_status)
        final_queue = extract_queue_info(final_status)
        
        print("  并发状态:")
        print(f"    初始并发任务: {initial_concurrent.get('current_concurrent_tasks', 'N/A')}")
        print(f"    最终并发任务: {final_concurrent.get('current_concurrent_tasks', 'N/A')}")
        print(f"    最大并发限制: {final_concurrent.get('max_concurrent_tasks', 'N/A')}")
        print(f"    最终利用率: {final_concurrent.get('concurrent_utilization', 'N/A')}")
        
        print("  队列状态变化:")
        for key in ['pending', 'processing', 'completed', 'failed']:
            initial_val = initial_queue.get(key, 0)
            final_val = final_queue.get(key, 0)
            change = final_val - initial_val
            print(f"    {key.capitalize()}: {initial_val} → {final_val} ({change:+d})")
        
        # 测试结论
        print(f"\n🎯 测试结论:")
        
        if success_rate >= 95:
            print("  ✅ 系统稳定性优秀（成功率 ≥ 95%）")
        elif success_rate >= 90:
            print("  ⚠️  系统稳定性良好（成功率 ≥ 90%）")
        else:
            print("  ❌ 系统稳定性需要改进（成功率 < 90%）")
        
        if actual_qps >= target_qps * 0.8:
            print("  ✅ 系统吞吐量达标（≥ 80% 目标QPS）")
        else:
            print("  ⚠️  系统吞吐量未达标（< 80% 目标QPS）")
        
        if upload_times and statistics.mean(upload_times) < 5.0:
            print("  ✅ 系统响应时间优秀（平均 < 5秒）")
        elif upload_times and statistics.mean(upload_times) < 10.0:
            print("  ⚠️  系统响应时间一般（平均 < 10秒）")
        else:
            print("  ❌ 系统响应时间较慢（平均 ≥ 10秒）")
        
        return {
            "total_files": total_files,
            "successful_uploads": successful_uploads,
            "failed_uploads": failed_uploads,
            "success_rate": success_rate,
            "test_duration": test_duration,
            "actual_qps": actual_qps,
            "target_qps": target_qps,
            "avg_response_time": statistics.mean(upload_times) if upload_times else 0,
            "initial_status": initial_status,
            "final_status": final_status
        }
    
    def cleanup_test_directory(self):
        """清理测试目录"""
        try:
            if os.path.exists(TEST_FILES_DIR):
                # 删除目录中的所有文件
                for filename in os.listdir(TEST_FILES_DIR):
                    file_path = os.path.join(TEST_FILES_DIR, filename)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                
                # 删除目录
                os.rmdir(TEST_FILES_DIR)
                print(f"\n🧹 已清理测试目录: {TEST_FILES_DIR}")
        except Exception as e:
            print(f"\n⚠️  清理测试目录失败: {e}")


async def main():
    """主函数"""
    print("🧪 性能压力测试")
    print("="*40)
    
    # 检查服务器是否可用
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/") as response:
                if response.status != 200:
                    print(f"❌ 服务器不可用: {BASE_URL}")
                    sys.exit(1)
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        sys.exit(1)
    
    print(f"✅ 服务器连接正常: {BASE_URL}")
    
    # 运行压力测试
    async with StressTestRunner() as tester:
        # 轻量级压力测试
        await tester.run_stress_test(
            concurrent_connections=8,  # 8个并发连接
            files_per_worker=3,        # 每个工作线程3个文件
            target_qps=4,              # 目标4 QPS
            duration_minutes=2         # 持续2分钟
        )
    
    print("\n🎉 性能压力测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
