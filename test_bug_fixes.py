#!/usr/bin/env python3
"""
测试修复的bug
Test Bug Fixes

验证以下修复：
1. bucket_name配置问题
2. 解析后md文档上传到MinIO功能
3. Dify上传文档名称格式
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.config.settings import video_api_settings, document_api_settings
from src.core.dependencies import get_minio_service, get_dify_service
from src.document_rag.ali_service import AlibabaCloudService
from src.config.settings import alibaba_cloud_settings


async def test_bucket_name_configuration():
    """测试bucket_name配置"""
    print("🔍 测试bucket_name配置...")
    
    # 测试视频API配置
    print(f"视频API bucket_name: {video_api_settings.BUCKET_NAME}")
    
    # 测试文档API配置
    print(f"文档API bucket_name: {document_api_settings.BUCKET_NAME}")
    
    # 验证配置可以从环境变量读取
    import os
    original_bucket = os.environ.get('BUCKET_NAME')
    
    # 临时设置环境变量
    os.environ['BUCKET_NAME'] = 'test-bucket'
    
    # 重新导入配置
    from src.config.settings import VideoAPISettings, DocumentAPISettings
    test_video_settings = VideoAPISettings()
    test_doc_settings = DocumentAPISettings()
    
    print(f"测试环境变量读取 - 视频: {test_video_settings.BUCKET_NAME}")
    print(f"测试环境变量读取 - 文档: {test_doc_settings.BUCKET_NAME}")
    
    # 恢复原始环境变量
    if original_bucket:
        os.environ['BUCKET_NAME'] = original_bucket
    else:
        os.environ.pop('BUCKET_NAME', None)
    
    print("✅ bucket_name配置测试完成")


async def test_minio_service():
    """测试MinIO服务"""
    print("🔍 测试MinIO服务...")
    
    try:
        minio_service = get_minio_service()
        
        # 测试连接
        print("测试MinIO连接...")
        # 这里可以添加实际的连接测试
        
        print("✅ MinIO服务测试完成")
        
    except Exception as e:
        print(f"❌ MinIO服务测试失败: {e}")


async def test_markdown_filename_generation():
    """测试markdown文件名生成逻辑"""
    print("🔍 测试markdown文件名生成...")
    
    test_cases = [
        ("document.pdf", "document.md"),
        ("presentation.pptx", "presentation.md"),
        ("report.docx", "report.md"),
        ("file_without_extension", "file_without_extension.md"),
        ("file.with.multiple.dots.pdf", "file.with.multiple.dots.md"),
    ]
    
    for original, expected in test_cases:
        # 模拟文档处理器中的逻辑
        base_filename = original.rsplit('.', 1)[0] if '.' in original else original
        md_filename = f"{base_filename}.md"
        
        assert md_filename == expected, f"期望: {expected}, 实际: {md_filename}"
        print(f"✓ {original} -> {md_filename}")
    
    print("✅ markdown文件名生成测试完成")


async def test_minio_path_generation():
    """测试MinIO路径生成"""
    print("🔍 测试MinIO路径生成...")
    
    test_cases = [
        ("document.pdf", "parsed_markdowns/document.md"),
        ("presentation.pptx", "parsed_markdowns/presentation.md"),
        ("report.docx", "parsed_markdowns/report.md"),
    ]
    
    for original_filename, expected_path in test_cases:
        # 模拟文档处理器中的逻辑
        base_filename = original_filename.rsplit('.', 1)[0] if '.' in original_filename else original_filename
        md_filename = f"{base_filename}.md"
        object_name = f"parsed_markdowns/{md_filename}"
        
        assert object_name == expected_path, f"期望: {expected_path}, 实际: {object_name}"
        print(f"✓ {original_filename} -> {object_name}")
    
    print("✅ MinIO路径生成测试完成")


async def test_dify_service():
    """测试Dify服务"""
    print("🔍 测试Dify服务...")
    
    try:
        dify_service = get_dify_service()
        
        # 测试服务初始化
        print(f"Dify API Base: {dify_service.base_url}")
        print(f"Dify API Key: {dify_service.api_key[:10]}...")
        
        print("✅ Dify服务测试完成")
        
    except Exception as e:
        print(f"❌ Dify服务测试失败: {e}")


async def test_alibaba_service():
    """测试阿里云服务"""
    print("🔍 测试阿里云服务...")
    
    try:
        alibaba_service = AlibabaCloudService(alibaba_cloud_settings)
        
        # 测试服务初始化
        print("阿里云服务初始化成功")
        
        print("✅ 阿里云服务测试完成")
        
    except Exception as e:
        print(f"❌ 阿里云服务测试失败: {e}")


async def main():
    """主测试函数"""
    print("🚀 开始测试bug修复...")
    print("=" * 50)
    
    # 测试配置
    await test_bucket_name_configuration()
    print()
    
    # 测试文件名生成
    await test_markdown_filename_generation()
    print()
    
    # 测试路径生成
    await test_minio_path_generation()
    print()
    
    # 测试服务
    await test_minio_service()
    print()
    
    await test_dify_service()
    print()
    
    await test_alibaba_service()
    print()
    
    print("=" * 50)
    print("🎉 所有测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
