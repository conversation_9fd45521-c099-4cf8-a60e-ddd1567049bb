#!/usr/bin/env python3
"""
测试Dify状态检查修复
Test Dify Status Check Fix

验证修复后的Dify状态检查功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.core.dependencies import get_dify_service
from src.config.settings import dify_settings


async def test_dify_status_methods():
    """测试Dify状态检查方法"""
    print("🔍 测试Dify状态检查方法...")
    
    try:
        dify_service = get_dify_service()
        
        # 1. 测试知识库列表
        print("\n1. 获取知识库列表...")
        datasets = await dify_service.get_datasets()
        if datasets["success"]:
            print(f"✅ 成功获取 {len(datasets['data'])} 个知识库")
            
            if datasets["data"]:
                # 选择第一个知识库进行测试
                test_dataset = datasets["data"][0]
                dataset_id = test_dataset["id"]
                dataset_name = test_dataset["name"]
                print(f"📁 测试知识库: {dataset_name} (ID: {dataset_id})")
                
                # 2. 测试批量状态同步
                print(f"\n2. 批量同步知识库状态...")
                batch_result = await dify_service.batch_sync_documents_status(dataset_id)
                if batch_result["success"]:
                    summary = batch_result["summary"]
                    print(f"✅ 批量同步成功:")
                    print(f"   总文档数: {summary['total']}")
                    print(f"   已完成: {summary['completed']}")
                    print(f"   索引中: {summary['indexing']}")
                    print(f"   等待中: {summary['waiting']}")
                    print(f"   错误: {summary['error']}")
                    print(f"   其他: {summary['other']}")
                    
                    # 3. 测试单个文档状态检查
                    documents = batch_result["documents"]
                    if documents:
                        test_doc = documents[0]
                        doc_id = test_doc["id"]
                        doc_name = test_doc["name"]
                        print(f"\n3. 测试单个文档状态检查...")
                        print(f"📄 测试文档: {doc_name} (ID: {doc_id})")
                        
                        # 通过document_id检查状态
                        doc_status = await dify_service.get_document_status_by_id(dataset_id, doc_id)
                        if doc_status["success"]:
                            print(f"✅ 文档状态检查成功:")
                            print(f"   状态: {doc_status['indexing_status']}")
                            print(f"   词数: {doc_status.get('word_count', 0)}")
                            print(f"   令牌数: {doc_status.get('tokens', 0)}")
                            print(f"   启用: {doc_status.get('enabled', False)}")
                        else:
                            print(f"❌ 文档状态检查失败: {doc_status['error']}")
                        
                        # 4. 测试综合状态同步
                        print(f"\n4. 测试综合状态同步...")
                        sync_result = await dify_service.sync_document_status(
                            dataset_id=dataset_id,
                            document_id=doc_id
                        )
                        if sync_result["success"]:
                            print(f"✅ 综合状态同步成功:")
                            print(f"   使用的方法: {sync_result.get('sync_methods_used', [])}")
                            print(f"   尝试的方法数: {sync_result.get('total_methods_tried', 0)}")
                            print(f"   状态: {sync_result.get('indexing_status', 'unknown')}")
                        else:
                            print(f"❌ 综合状态同步失败: {sync_result['error']}")
                    
                    else:
                        print("⚠️ 知识库中没有文档可供测试")
                else:
                    print(f"❌ 批量同步失败: {batch_result['error']}")
            else:
                print("⚠️ 没有可用的知识库进行测试")
        else:
            print(f"❌ 获取知识库列表失败: {datasets['error']}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")


async def test_api_response_parsing():
    """测试API响应解析"""
    print("\n🧪 测试API响应解析...")
    
    # 模拟API响应数据
    mock_response = {
        "data": [{
            "id": "test-doc-id",
            "indexing_status": "indexing",
            "processing_started_at": 1681623462.0,
            "parsing_completed_at": 1681623462.0,
            "cleaning_completed_at": 1681623462.0,
            "splitting_completed_at": 1681623462.0,
            "completed_at": None,
            "paused_at": None,
            "error": None,
            "stopped_at": None,
            "completed_segments": 24,
            "total_segments": 100
        }]
    }
    
    try:
        # 模拟解析逻辑
        data_list = mock_response.get("data", [])
        if data_list:
            doc_status = data_list[0]
            indexing_status = doc_status.get("indexing_status", "unknown")
            completed_segments = doc_status.get("completed_segments", 0)
            total_segments = doc_status.get("total_segments", 0)
            
            # 计算进度
            if total_segments > 0:
                progress = (completed_segments / total_segments) * 100
            else:
                progress = 0
            
            print(f"✅ API响应解析成功:")
            print(f"   文档ID: {doc_status.get('id')}")
            print(f"   状态: {indexing_status}")
            print(f"   进度: {progress:.1f}% ({completed_segments}/{total_segments})")
            print(f"   开始时间: {doc_status.get('processing_started_at')}")
            print(f"   错误信息: {doc_status.get('error', '无')}")
        else:
            print("❌ 模拟响应中没有data数组")
            
    except Exception as e:
        print(f"❌ API响应解析测试失败: {e}")


async def test_status_mapping():
    """测试状态映射"""
    print("\n🗺️ 测试状态映射...")
    
    # 测试各种状态的处理
    test_statuses = [
        "waiting",
        "indexing", 
        "processing",
        "completed",
        "success",
        "error",
        "failed",
        "stopped",
        "paused",
        "unknown"
    ]
    
    for status in test_statuses:
        # 模拟状态处理逻辑
        if status in ["completed", "success"]:
            result = "✅ 完成"
        elif status in ["indexing", "processing"]:
            result = "⏳ 处理中"
        elif status in ["error", "failed", "stopped"]:
            result = "❌ 失败"
        elif status in ["waiting", "queuing"]:
            result = "⏸️ 等待"
        elif status == "paused":
            result = "⏸️ 暂停"
        else:
            result = "❓ 未知"
        
        print(f"   {status:12} -> {result}")


async def main():
    """主测试函数"""
    print("🚀 开始测试Dify状态检查修复...")
    print("=" * 60)
    
    # 测试API响应解析
    await test_api_response_parsing()
    
    # 测试状态映射
    await test_status_mapping()
    
    # 测试实际的Dify服务方法
    await test_dify_status_methods()
    
    print("\n" + "=" * 60)
    print("🎉 所有测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
