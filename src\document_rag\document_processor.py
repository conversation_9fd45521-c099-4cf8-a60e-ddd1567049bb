"""
文档处理后台服务
Document Processing Background Service

实现阿里云文档解析和Dify向量化的完整流程
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

# 导入模型
from src.models.document import Document, DocumentBatch, TaskStatus, BatchStatus, AliTaskStatus, DifyIndexingStatus

# 导入服务
from src.core.dependencies import (
    get_minio_service,
    get_dify_service,
    get_alibaba_cloud_service
)


class DocumentProcessor:
    """文档处理器"""

    def __init__(self, minio_service=None, dify_service=None, alibaba_service=None, dify_settings=None):
        """
        初始化文档处理器

        Args:
            minio_service: MinIO服务实例，如果为None则使用依赖注入获取
            dify_service: Dify服务实例，如果为None则使用依赖注入获取
            alibaba_service: 阿里云服务实例，如果为None则使用依赖注入获取
            dify_settings: Dify配置实例，如果为None则使用依赖注入获取
        """
        # 使用传入的服务实例，如果没有则通过依赖注入获取
        self.minio_service = minio_service or get_minio_service()
        self.dify_service = dify_service or get_dify_service()
        self.alibaba_service = alibaba_service or get_alibaba_cloud_service()

        # 获取Dify配置
        if dify_settings is None:
            from src.config.settings import dify_settings as default_dify_settings
            self.dify_settings = default_dify_settings
        else:
            self.dify_settings = dify_settings
        
    async def process_document(self, document_uuid: str) -> Dict[str, Any]:
        """
        处理单个文档的完整流程
        
        Args:
            document_uuid: 文档UUID
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        
        try:
            # 获取文档记录
            document = await Document.filter(uuid=document_uuid).first()
            if not document:
                return {
                    "success": False,
                    "error": "文档不存在",
                    "document_uuid": document_uuid
                }
            
            logger.info(f"开始处理文档: {document.original_filename}")

            # 阶段一：阿里云文档解析
            ali_result = await self._process_alibaba_parsing(document)
            if not ali_result["success"]:
                error_msg = f"阿里云解析失败: {ali_result['error']}"
                await self._mark_document_failed(document, error_msg)
                logger.error(f"❌ {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "document_uuid": document_uuid,
                    "stage": "alibaba_parsing"
                }

            # 阶段二：Dify向量化
            dify_result = await self._process_dify_indexing(document, ali_result["markdown_content"])
            if not dify_result["success"]:
                error_msg = f"Dify向量化失败: {dify_result['error']}"
                await self._mark_document_failed(document, error_msg)
                logger.error(f"❌ {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "document_uuid": document_uuid,
                    "stage": "dify_indexing"
                }
            
            # 标记完成
            await self._mark_document_completed(document)
            
            # 更新批次状态
            if document.batch_id:
                await self._update_batch_status(document.batch_id)
            
            return {
                "success": True,
                "message": "文档处理完成",
                "document_uuid": document_uuid,
                "filename": document.original_filename
            }
            
        except Exception as e:
            error_msg = f"文档处理异常: {str(e)}"
            logger.error(error_msg, exc_info=True)

            # 尝试标记文档失败
            try:
                document = await Document.filter(uuid=document_uuid).first()
                if document:
                    await self._mark_document_failed(document, error_msg)
            except:
                pass

            return {
                "success": False,
                "error": error_msg,
                "document_uuid": document_uuid
            }

    async def _determine_current_stage(self, document: Document) -> str:
        """
        确定文档当前的处理阶段

        Args:
            document: 文档实例

        Returns:
            str: 当前阶段 (alibaba_parsing, dify_indexing, completed)
        """
        # 如果已经完成
        if document.task_status == TaskStatus.COMPLETED:
            return "completed"

        # 如果阿里云解析还未开始或失败，从阿里云解析开始
        if (document.ali_task_status in [AliTaskStatus.PENDING, AliTaskStatus.FAILED] or
            not document.ali_task_id):
            return "alibaba_parsing"

        # 如果阿里云解析完成，但Dify向量化还未开始或失败
        if (document.ali_task_status == AliTaskStatus.SUCCESS and
            (document.dify_indexing_status in [DifyIndexingStatus.PENDING, DifyIndexingStatus.ERROR] or
             not document.dify_dataset_id)):
            return "dify_indexing"

        # 如果阿里云解析正在进行中
        if document.ali_task_status == AliTaskStatus.PROCESSING:
            return "alibaba_parsing"

        # 如果Dify向量化正在进行中
        if document.dify_indexing_status == DifyIndexingStatus.INDEXING:
            return "dify_indexing"

        # 默认从阿里云解析开始
        return "alibaba_parsing"

    async def _process_alibaba_parsing_async(self, document: Document) -> Dict[str, Any]:
        """
        异步处理阿里云文档解析（非阻塞版本）

        Args:
            document: 文档实例

        Returns:
            Dict[str, Any]: 解析结果，包含completed字段表示是否完成
        """
        try:
            # 如果还没有提交任务，先提交
            if not document.ali_task_id or document.ali_task_status == AliTaskStatus.PENDING:
                # 更新状态为解析中
                document.task_status = TaskStatus.PROCESSING
                document.ali_task_status = AliTaskStatus.PROCESSING
                await document.save()

                logger.info(f"提交阿里云解析任务: {document.original_filename}")

                # 提交解析任务
                submit_result = await self.alibaba_service.submit_file(
                    file_url=document.minio_path,
                    file_name=document.original_filename
                )

                if not submit_result["success"]:
                    return {
                        "success": False,
                        "error": f"阿里云任务提交失败: {submit_result['error']}"
                    }

                # 保存任务ID
                document.ali_task_id = submit_result["job_id"]
                await document.save()

                # 任务刚提交，还未完成
                return {
                    "success": True,
                    "completed": False,
                    "message": "阿里云解析任务已提交"
                }

            # 检查任务状态
            status_result = await self.alibaba_service.check_job_status(document.ali_task_id)

            if not status_result["success"]:
                return {
                    "success": False,
                    "error": f"检查阿里云任务状态失败: {status_result['error']}"
                }

            if status_result["is_completed"]:
                if status_result["is_success"]:
                    # 获取解析结果
                    result = await self.alibaba_service.get_job_result(document.ali_task_id)
                    if result["success"] and result["result"]:
                        # 处理图片URL替换
                        markdown_content = result["result"]
                        from src.config.settings import document_api_settings
                        processed_content = await self.alibaba_service.download_images_from_markdown(
                            markdown_content, self.minio_service, document.original_filename, document_api_settings.BUCKET_NAME
                        )

                        # 上传解析后的markdown文档到MinIO
                        await self._upload_markdown_to_minio(document, processed_content, document_api_settings.BUCKET_NAME)

                        # 更新状态
                        document.ali_task_status = AliTaskStatus.SUCCESS
                        await document.save()

                        return {
                            "success": True,
                            "completed": True,
                            "markdown_content": processed_content
                        }
                    else:
                        return {
                            "success": False,
                            "error": "获取阿里云解析结果失败"
                        }
                else:
                    return {
                        "success": False,
                        "error": "阿里云文档解析失败"
                    }
            else:
                # 任务还在进行中
                return {
                    "success": True,
                    "completed": False,
                    "message": "阿里云解析进行中"
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"阿里云解析异常: {str(e)}"
            }

    async def _process_dify_indexing_async(self, document: Document) -> Dict[str, Any]:
        """
        异步处理Dify向量化（非阻塞版本）

        Args:
            document: 文档实例

        Returns:
            Dict[str, Any]: 向量化结果，包含completed字段表示是否完成
        """
        try:
            # 如果还没有提交任务，先提交
            if not document.dify_dataset_id or document.dify_indexing_status == DifyIndexingStatus.PENDING:
                # 更新状态为向量化中
                document.task_status = TaskStatus.INDEXING
                document.dify_indexing_status = DifyIndexingStatus.INDEXING
                await document.save()

                logger.info(f"提交Dify向量化任务: {document.original_filename}")

                # 确保知识库存在
                from src.config.settings import dify_settings
                knowledge_base_name = dify_settings.DIFY_DOCUMENT_KNOWLEDGE_BASE
                kb_result = await self.dify_service.ensure_knowledge_base_exists(knowledge_base_name)

                if not kb_result["success"]:
                    return {
                        "success": False,
                        "error": f"创建Dify知识库失败: {kb_result['error']}"
                    }

                dataset_id = kb_result["dataset_id"]
                document.dify_dataset_id = dataset_id
                await document.save()

                # 获取markdown内容
                if document.parsed_markdown_path:
                    # 从MinIO获取markdown内容
                    try:
                        markdown_content = await self._get_markdown_from_minio(document)
                    except Exception as e:
                        return {
                            "success": False,
                            "error": f"获取markdown内容失败: {str(e)}"
                        }
                else:
                    return {
                        "success": False,
                        "error": "缺少解析后的markdown内容"
                    }

                # 生成以.md结尾的文档名称
                base_filename = document.original_filename.rsplit('.', 1)[0] if '.' in document.original_filename else document.original_filename
                md_filename = f"{base_filename}.md"

                # 提交文档到Dify
                submit_result = await self.dify_service.create_document_by_text(
                    dataset_id=dataset_id,
                    name=md_filename,
                    text=markdown_content
                )

                if not submit_result["success"]:
                    return {
                        "success": False,
                        "error": f"Dify文档创建失败: {submit_result['error']}"
                    }

                # 保存Dify相关ID
                document.dify_document_id = submit_result.get("document_id")
                document.dify_batch_id = submit_result.get("batch_id")
                await document.save()

                # 任务刚提交，还未完成
                return {
                    "success": True,
                    "completed": False,
                    "message": "Dify向量化任务已提交"
                }

            # 检查向量化状态
            dataset_id = document.dify_dataset_id
            if document.dify_batch_id:
                status_result = await self.dify_service.check_document_indexing_status(
                    dataset_id, document.dify_batch_id
                )

                if status_result["success"]:
                    indexing_status = status_result.get("indexing_status", "processing")
                    progress = status_result.get("progress_percentage", 0)

                    logger.info(f"向量化状态: {indexing_status}, 进度: {progress}%")

                    # 检查完成状态
                    if indexing_status in ["completed", "success"]:
                        document.dify_indexing_status = DifyIndexingStatus.COMPLETED
                        await document.save()
                        logger.info(f"✅ 向量化完成: {document.original_filename}")
                        return {
                            "success": True,
                            "completed": True,
                            "message": "向量化完成"
                        }

                    # 检查错误状态
                    elif indexing_status in ["error", "failed", "stopped"]:
                        error_msg = status_result.get("error", "向量化失败")
                        document.dify_indexing_status = DifyIndexingStatus.ERROR
                        await document.save()
                        return {
                            "success": False,
                            "error": f"Dify向量化失败: {error_msg}"
                        }

                    # 继续等待的状态
                    else:
                        return {
                            "success": True,
                            "completed": False,
                            "message": f"向量化进行中，进度: {progress}%"
                        }
                else:
                    # 尝试备用方法
                    if document.dify_document_id:
                        backup_result = await self.dify_service.get_document_status_by_id(
                            dataset_id, document.dify_document_id
                        )
                        if backup_result["success"]:
                            indexing_status = backup_result.get("indexing_status", "processing")
                            if indexing_status in ["completed", "success"]:
                                document.dify_indexing_status = DifyIndexingStatus.COMPLETED
                                await document.save()
                                return {
                                    "success": True,
                                    "completed": True,
                                    "message": "向量化完成（备用检查）"
                                }

                    return {
                        "success": True,
                        "completed": False,
                        "message": "状态检查失败，继续等待"
                    }
            else:
                return {
                    "success": False,
                    "error": "缺少batch_id，无法检查向量化状态"
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"Dify向量化异常: {str(e)}"
            }

    async def _get_markdown_from_minio(self, document: Document) -> str:
        """
        从MinIO获取markdown内容

        Args:
            document: 文档实例

        Returns:
            str: markdown内容
        """
        # 这里需要实现从MinIO获取文件内容的逻辑
        # 暂时返回空字符串，实际实现需要调用MinIO服务
        # TODO: 实现从MinIO获取markdown内容
        return ""

    async def _process_alibaba_parsing(self, document: Document) -> Dict[str, Any]:
        """
        处理阿里云文档解析
        
        Args:
            document: 文档实例
            
        Returns:
            Dict[str, Any]: 解析结果
        """
        
        try:
            # 更新状态为解析中
            document.task_status = TaskStatus.PROCESSING
            document.ali_task_status = AliTaskStatus.PROCESSING
            await document.save()
            
            logger.info(f"提交阿里云解析任务: {document.original_filename}")
            
            # 提交解析任务
            submit_result = await self.alibaba_service.submit_file(
                file_url=document.minio_path,
                file_name=document.original_filename
            )
            
            if not submit_result["success"]:
                return {
                    "success": False,
                    "error": f"阿里云任务提交失败: {submit_result['error']}"
                }
            
            # 保存任务ID
            document.ali_task_id = submit_result["job_id"]
            await document.save()
            
            # 轮询等待解析完成
            max_wait_time = 300  # 5分钟超时
            check_interval = 10  # 10秒检查一次
            waited_time = 0
            
            while waited_time < max_wait_time:
                await asyncio.sleep(check_interval)
                waited_time += check_interval
                
                # 检查任务状态
                status_result = await self.alibaba_service.check_job_status(document.ali_task_id)
                
                if not status_result["success"]:
                    return {
                        "success": False,
                        "error": f"检查阿里云任务状态失败: {status_result['error']}"
                    }
                
                if status_result["is_completed"]:
                    if status_result["is_success"]:
                        # 获取解析结果
                        result = await self.alibaba_service.get_job_result(document.ali_task_id)
                        if result["success"] and result["result"]:
                            # 处理图片URL替换
                            markdown_content = result["result"]
                            # 从document_api_settings获取bucket_name
                            from src.config.settings import document_api_settings
                            processed_content = await self.alibaba_service.download_images_from_markdown(
                                markdown_content, self.minio_service, document.original_filename, document_api_settings.BUCKET_NAME
                            )

                            # 上传解析后的markdown文档到MinIO的parsed_markdowns/文件夹
                            await self._upload_markdown_to_minio(document, processed_content, document_api_settings.BUCKET_NAME)

                            # 更新状态
                            document.ali_task_status = AliTaskStatus.SUCCESS
                            await document.save()

                            return {
                                "success": True,
                                "markdown_content": processed_content
                            }
                        else:
                            return {
                                "success": False,
                                "error": "获取阿里云解析结果失败"
                            }
                    else:
                        return {
                            "success": False,
                            "error": "阿里云文档解析失败"
                        }
            
            # 超时
            return {
                "success": False,
                "error": "阿里云文档解析超时"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"阿里云解析异常: {str(e)}"
            }
    
    async def _process_dify_indexing(self, document: Document, markdown_content: str) -> Dict[str, Any]:
        """
        处理Dify向量化
        
        Args:
            document: 文档实例
            markdown_content: Markdown内容
            
        Returns:
            Dict[str, Any]: 向量化结果
        """
        
        try:
            # 更新状态为向量化中
            document.task_status = TaskStatus.INDEXING
            document.dify_indexing_status = DifyIndexingStatus.INDEXING
            await document.save()
            
            logger.info(f"提交Dify向量化任务: {document.original_filename}")
            
            # 确保知识库存在
            from src.config.settings import dify_settings
            knowledge_base_name = dify_settings.DIFY_DOCUMENT_KNOWLEDGE_BASE
            kb_result = await self.dify_service.ensure_knowledge_base_exists(knowledge_base_name)
            
            if not kb_result["success"]:
                return {
                    "success": False,
                    "error": f"创建Dify知识库失败: {kb_result['error']}"
                }
            
            dataset_id = kb_result["dataset_id"]
            document.dify_dataset_id = dataset_id
            await document.save()
            
            # 生成以.md结尾的文档名称
            base_filename = document.original_filename.rsplit('.', 1)[0] if '.' in document.original_filename else document.original_filename
            md_filename = f"{base_filename}.md"

            # 提交文档到Dify
            submit_result = await self.dify_service.create_document_by_text(
                dataset_id=dataset_id,
                name=md_filename,
                text=markdown_content
            )
            
            if not submit_result["success"]:
                return {
                    "success": False,
                    "error": f"Dify文档创建失败: {submit_result['error']}"
                }
            
            # 保存Dify相关ID
            document.dify_document_id = submit_result.get("document_id")
            document.dify_batch_id = submit_result.get("batch_id")
            await document.save()
            
            # 改进的轮询等待向量化完成
            max_wait_time = 600  # 10分钟超时（增加超时时间）
            check_interval = 15  # 15秒检查一次（减少检查频率）
            waited_time = 0
            consecutive_errors = 0
            max_consecutive_errors = 3  # 最多连续3次错误

            logger.info(f"开始轮询Dify向量化状态，最大等待时间: {max_wait_time}秒")

            while waited_time < max_wait_time:
                await asyncio.sleep(check_interval)
                waited_time += check_interval

                try:
                    # 检查向量化状态
                    if document.dify_batch_id:
                        status_result = await self.dify_service.check_document_indexing_status(
                            dataset_id, document.dify_batch_id
                        )

                        if status_result["success"]:
                            # 重置错误计数
                            consecutive_errors = 0

                            indexing_status = status_result.get("indexing_status", "processing")
                            progress = status_result.get("progress_percentage", 0)
                            completed_segments = status_result.get("completed_segments", 0)
                            total_segments = status_result.get("total_segments", 0)

                            logger.info(f"向量化状态: {indexing_status}, 进度: {progress}% ({completed_segments}/{total_segments})")

                            # 检查各种完成状态
                            if indexing_status in ["completed", "success"]:
                                document.dify_indexing_status = DifyIndexingStatus.COMPLETED
                                await document.save()
                                logger.info(f"✅ 向量化完成: {document.original_filename}")
                                return {"success": True, "message": "向量化完成"}

                            # 检查错误状态
                            elif indexing_status in ["error", "failed", "stopped"]:
                                error_msg = status_result.get("error", "向量化失败")
                                document.dify_indexing_status = DifyIndexingStatus.ERROR
                                await document.save()
                                return {
                                    "success": False,
                                    "error": f"Dify向量化失败: {error_msg}"
                                }

                            # 检查暂停状态
                            elif indexing_status == "paused":
                                logger.warning(f"⏸️ 向量化已暂停: {document.original_filename}")
                                return {
                                    "success": False,
                                    "error": "向量化任务已暂停"
                                }

                            # 继续等待的状态：waiting, indexing, processing
                            else:
                                logger.debug(f"⏳ 继续等待向量化完成，已等待: {waited_time}秒")

                        else:
                            # API调用失败
                            consecutive_errors += 1
                            error_msg = status_result.get("error", "未知错误")
                            logger.error(f"❌ 状态检查失败 ({consecutive_errors}/{max_consecutive_errors}): {error_msg}")

                            # 如果连续错误次数过多，尝试备用方法
                            if consecutive_errors >= max_consecutive_errors:
                                logger.info("🔄 尝试备用状态检查方法...")
                                if document.dify_document_id:
                                    backup_result = await self.dify_service.get_document_status_by_id(
                                        dataset_id, document.dify_document_id
                                    )
                                    if backup_result["success"]:
                                        indexing_status = backup_result.get("indexing_status", "processing")
                                        if indexing_status in ["completed", "success"]:
                                            document.dify_indexing_status = DifyIndexingStatus.COMPLETED
                                            await document.save()
                                            return {"success": True, "message": "向量化完成（备用检查）"}
                                        elif indexing_status in ["error", "failed"]:
                                            return {
                                                "success": False,
                                                "error": "Dify向量化失败（备用检查）"
                                            }

                                # 重置错误计数，继续尝试
                                consecutive_errors = 0

                    else:
                        logger.warning("⚠️ 缺少batch_id，无法检查状态")
                        return {
                            "success": False,
                            "error": "缺少batch_id，无法检查向量化状态"
                        }

                except Exception as e:
                    consecutive_errors += 1
                    logger.error(f"❌ 状态检查异常 ({consecutive_errors}/{max_consecutive_errors}): {str(e)}")

                    if consecutive_errors >= max_consecutive_errors:
                        return {
                            "success": False,
                            "error": f"状态检查连续失败: {str(e)}"
                        }

            # 超时处理
            logger.warning(f"⏰ 向量化状态检查超时（{max_wait_time}秒），但任务可能仍在后台处理")
            # 不标记为失败，因为任务可能仍在处理中
            return {
                "success": True,
                "message": f"向量化状态检查超时，但任务可能仍在处理中。请稍后手动检查状态。",
                "timeout": True
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Dify向量化异常: {str(e)}"
            }
    
    async def _mark_document_failed(self, document: Document, error_message: str):
        """
        标记文档处理失败

        Args:
            document: 文档实例
            error_message: 错误信息
        """
        try:
            # 更新文档状态
            document.task_status = TaskStatus.FAILED
            document.error_message = error_message[:2000]  # 限制错误信息长度

            # 根据当前状态设置具体的失败状态
            if document.ali_task_status == AliTaskStatus.PROCESSING:
                document.ali_task_status = AliTaskStatus.FAILED
            elif document.dify_indexing_status == DifyIndexingStatus.INDEXING:
                document.dify_indexing_status = DifyIndexingStatus.ERROR

            await document.save()

            logger.error(f"❌ 文档处理失败: {document.original_filename} - {error_message}")

            # 更新批次状态
            if document.batch_id:
                await self._update_batch_status(document.batch_id)

        except Exception as e:
            logger.error(f"标记文档失败时出错: {e}", exc_info=True)
    
    async def _upload_markdown_to_minio(self, document: Document, markdown_content: str, bucket_name: str):
        """
        将解析后的markdown文档上传到MinIO的parsed_markdowns/文件夹

        Args:
            document: 文档实例
            markdown_content: markdown内容
            bucket_name: 存储桶名称
        """
        try:
            # 生成markdown文件名，确保以.md结尾
            base_filename = document.original_filename.rsplit('.', 1)[0] if '.' in document.original_filename else document.original_filename
            md_filename = f"{base_filename}.md"
            object_name = f"parsed_markdowns/{md_filename}"

            # 将markdown内容转换为字节流
            markdown_bytes = markdown_content.encode('utf-8')

            # 上传到MinIO
            minio_url = await self.minio_service.upload_file(
                bucket_name=bucket_name,
                object_name=object_name,
                file_stream=markdown_bytes,
                length=len(markdown_bytes),
                content_type="text/markdown"
            )

            # 保存markdown文档的MinIO路径到数据库
            document.parsed_markdown_path = minio_url
            await document.save()

            logger.info(f"✅ Markdown文档已上传到MinIO: {object_name}")

        except Exception as e:
            logger.error(f"❌ 上传Markdown文档到MinIO失败: {e}", exc_info=True)
            # 不抛出异常，因为这不应该影响主要的处理流程

    async def _mark_document_completed(self, document: Document):
        """
        标记文档处理完成

        Args:
            document: 文档实例
        """
        try:
            document.task_status = TaskStatus.COMPLETED
            document.ali_task_status = AliTaskStatus.SUCCESS
            document.dify_indexing_status = DifyIndexingStatus.COMPLETED
            document.error_message = None
            await document.save()

            logger.info(f"✅ 文档处理完成: {document.original_filename}")

        except Exception as e:
            logger.error(f"标记文档完成时出错: {e}", exc_info=True)
    
    async def _update_batch_status(self, batch_id: int):
        """更新批次状态"""
        try:
            batch = await DocumentBatch.get(id=batch_id)
            documents = await Document.filter(batch_id=batch_id).all()
            
            total_files = len(documents)
            completed_files = len([d for d in documents if d.task_status == TaskStatus.COMPLETED])
            failed_files = len([d for d in documents if d.task_status == TaskStatus.FAILED])
            processing_files = total_files - completed_files - failed_files
            
            # 更新统计
            batch.completed_files = completed_files
            batch.failed_files = failed_files
            
            # 更新批次状态
            if processing_files == 0:
                if failed_files == 0:
                    batch.batch_status = BatchStatus.COMPLETED
                elif failed_files == total_files:
                    batch.batch_status = BatchStatus.FAILED
                else:
                    batch.batch_status = BatchStatus.PARTIAL_FAILED
            else:
                batch.batch_status = BatchStatus.PROCESSING
            
            await batch.save()
            
        except Exception as e:
            logger.error(f"更新批次状态失败: {e}", exc_info=True)

    async def _process_document_legacy(self, document_uuid: str) -> Dict[str, Any]:
        """
        处理单个文档的完整流程（原始阻塞版本）

        Args:
            document_uuid: 文档UUID

        Returns:
            Dict[str, Any]: 处理结果
        """

        try:
            # 获取文档记录
            document = await Document.filter(uuid=document_uuid).first()
            if not document:
                return {
                    "success": False,
                    "error": "文档不存在",
                    "document_uuid": document_uuid
                }

            logger.info(f"开始处理文档（阻塞模式）: {document.original_filename}")

            # 阶段一：阿里云文档解析
            ali_result = await self._process_alibaba_parsing(document)
            if not ali_result["success"]:
                error_msg = f"阿里云解析失败: {ali_result['error']}"
                await self._mark_document_failed(document, error_msg)
                logger.error(f"❌ {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "document_uuid": document_uuid,
                    "stage": "alibaba_parsing"
                }

            # 阶段二：Dify向量化
            dify_result = await self._process_dify_indexing(document, ali_result["markdown_content"])
            if not dify_result["success"]:
                error_msg = f"Dify向量化失败: {dify_result['error']}"
                await self._mark_document_failed(document, error_msg)
                logger.error(f"❌ {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "document_uuid": document_uuid,
                    "stage": "dify_indexing"
                }

            # 标记完成
            await self._mark_document_completed(document)

            # 更新批次状态
            if document.batch_id:
                await self._update_batch_status(document.batch_id)

            return {
                "success": True,
                "message": "文档处理完成",
                "document_uuid": document_uuid,
                "filename": document.original_filename
            }

        except Exception as e:
            error_msg = f"文档处理异常: {str(e)}"
            logger.error(error_msg, exc_info=True)

            # 尝试标记文档失败
            try:
                document = await Document.filter(uuid=document_uuid).first()
                if document:
                    await self._mark_document_failed(document, error_msg)
            except:
                pass

            return {
                "success": False,
                "error": error_msg,
                "document_uuid": document_uuid
            }


# ===== Redis任务处理器 =====

async def process_document_task(task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理文档任务的函数，用于Redis任务队列（优化并发版本）

    Args:
        task_data: 任务数据，包含document_uuid等信息

    Returns:
        Dict[str, Any]: 处理结果
    """
    try:
        document_uuid = task_data.get("document_uuid")
        if not document_uuid:
            return {
                "success": False,
                "error": "缺少document_uuid参数"
            }

        # 创建文档处理器实例
        from src.core.dependencies import get_document_processor, get_redis_queue_service
        processor = get_document_processor()
        redis_queue = get_redis_queue_service()

        # 处理文档
        result = await processor.process_document(document_uuid)

        # 如果任务还在进行中，重新入队等待下次处理
        if result.get("in_progress"):
            logger.info(f"📋 任务进行中，重新入队: {document_uuid}")

            # 延迟重新入队（避免过于频繁的检查）
            import asyncio
            await asyncio.sleep(30)  # 等待30秒后重新入队

            # 重新推送任务到队列
            await redis_queue.push_task({
                "document_uuid": document_uuid,
                "filename": task_data.get("filename", "unknown"),
                "batch_id": task_data.get("batch_id"),
                "retry_count": task_data.get("retry_count", 0) + 1
            })

            return {
                "success": True,
                "message": f"任务进行中，已重新入队: {result.get('message', '')}",
                "document_uuid": document_uuid,
                "requeued": True
            }

        return result

    except Exception as e:
        return {
            "success": False,
            "error": f"任务处理异常: {str(e)}"
        }


async def process_document_task_legacy(task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理文档任务的函数（原始阻塞版本，保留作为备用）

    Args:
        task_data: 任务数据，包含document_uuid等信息

    Returns:
        Dict[str, Any]: 处理结果
    """
    try:
        document_uuid = task_data.get("document_uuid")
        if not document_uuid:
            return {
                "success": False,
                "error": "缺少document_uuid参数"
            }

        # 创建文档处理器实例
        from src.core.dependencies import get_document_processor
        processor = get_document_processor()

        # 使用原始的阻塞式处理方法
        result = await processor._process_document_legacy(document_uuid)

        return result

    except Exception as e:
        return {
            "success": False,
            "error": f"任务处理异常: {str(e)}"
        }
