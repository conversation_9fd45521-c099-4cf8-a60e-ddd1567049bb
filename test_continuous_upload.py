#!/usr/bin/env python3
"""
连续上传功能测试脚本
Test Continuous Upload Functionality

验证连续上传多个文件的功能，确保不会出现阻塞问题
"""

import asyncio
import aiohttp
import json
import time
import os
import sys
from pathlib import Path
from typing import List, Dict, Any

# 测试配置
BASE_URL = "http://localhost:8000"
UPLOAD_ENDPOINT = f"{BASE_URL}/api/v1/documents/upload"
STATUS_ENDPOINT = f"{BASE_URL}/api/v1/documents/concurrent/status"
QUEUE_STATUS_ENDPOINT = f"{BASE_URL}/api/v1/documents/queue/status"

# 测试文件配置
TEST_FILES_DIR = "src/docs/测试文档"


class ContinuousUploadTester:
    """连续上传测试器"""
    
    def __init__(self):
        self.session = None
        self.test_results = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def get_test_files(self, count: int = 5) -> List[str]:
        """获取测试文件"""
        print(f"📁 获取 {count} 个测试文件...")

        # 检查测试文件目录是否存在
        if not os.path.exists(TEST_FILES_DIR):
            print(f"❌ 测试文件目录不存在: {TEST_FILES_DIR}")
            return []

        # 获取目录中的所有PDF文件
        all_files = []
        for filename in os.listdir(TEST_FILES_DIR):
            if filename.lower().endswith('.pdf'):
                filepath = os.path.join(TEST_FILES_DIR, filename)
                all_files.append(filepath)

        if not all_files:
            print(f"❌ 测试文件目录中没有PDF文件: {TEST_FILES_DIR}")
            return []

        # 选择指定数量的文件（如果文件数量不足，就使用所有文件）
        selected_files = all_files[:count] if len(all_files) >= count else all_files

        print(f"  📄 找到 {len(all_files)} 个PDF文件，选择 {len(selected_files)} 个进行测试:")
        for filepath in selected_files:
            filename = os.path.basename(filepath)
            file_size = os.path.getsize(filepath) / 1024  # KB
            print(f"    ✅ {filename} ({file_size:.1f} KB)")

        return selected_files
    
    async def upload_file(self, file_path: str, batch_name: str = None) -> Dict[str, Any]:
        """上传单个文件"""
        filename = os.path.basename(file_path)

        try:
            with open(file_path, 'rb') as f:
                data = aiohttp.FormData()
                data.add_field('files', f, filename=filename, content_type='application/pdf')
                if batch_name:
                    data.add_field('batch_name', batch_name)

                start_time = time.time()
                async with self.session.post(UPLOAD_ENDPOINT, data=data) as response:
                    end_time = time.time()

                    result = {
                        "filename": filename,
                        "status_code": response.status,
                        "upload_time": end_time - start_time,
                        "success": response.status == 200
                    }

                    if response.status == 200:
                        response_data = await response.json()
                        result["response"] = response_data
                        result["batch_id"] = response_data.get("batch_id")
                    else:
                        result["error"] = await response.text()

                    return result

        except Exception as e:
            return {
                "filename": filename,
                "success": False,
                "error": str(e),
                "upload_time": 0
            }
    
    async def get_concurrent_status(self) -> Dict[str, Any]:
        """获取并发状态"""
        try:
            async with self.session.get(STATUS_ENDPOINT) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    # 暂时跳过监控API错误，返回模拟数据
                    return {"success": False, "error": "监控API暂时不可用", "mock": True}
        except Exception as e:
            return {"success": False, "error": str(e), "mock": True}

    async def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        try:
            async with self.session.get(QUEUE_STATUS_ENDPOINT) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    # 暂时跳过监控API错误，返回模拟数据
                    return {"success": False, "error": "队列监控API暂时不可用", "mock": True}
        except Exception as e:
            return {"success": False, "error": str(e), "mock": True}
    
    async def test_continuous_upload(self, file_count: int = 5, delay_between_uploads: float = 1.0):
        """测试连续上传功能"""
        print(f"\n🚀 开始连续上传测试 - {file_count} 个文件，间隔 {delay_between_uploads} 秒")
        
        # 获取测试文件
        file_paths = self.get_test_files(file_count)
        if not file_paths:
            print("❌ 没有可用的测试文件，测试终止")
            return []
        
        # 获取初始状态
        print("\n📊 获取初始状态...")
        initial_status = await self.get_concurrent_status()
        initial_queue = await self.get_queue_status()
        
        if not initial_status.get("mock"):
            print(f"初始并发任务数: {initial_status.get('concurrent_info', {}).get('current_concurrent_tasks', 'N/A')}")
        else:
            print("⚠️  监控API不可用，跳过并发状态检查")

        if not initial_queue.get("mock"):
            print(f"初始队列状态: {initial_queue.get('queue_stats', {})}")
        else:
            print("⚠️  队列监控API不可用，跳过队列状态检查")
        
        # 连续上传文件
        print(f"\n📤 开始连续上传 {file_count} 个文件...")
        upload_results = []
        
        for i, file_path in enumerate(file_paths):
            print(f"\n上传文件 {i+1}/{file_count}: {os.path.basename(file_path)}")
            
            # 上传文件
            result = await self.upload_file(file_path, f"连续上传测试_{int(time.time())}")
            upload_results.append(result)
            
            # 显示上传结果
            if result["success"]:
                print(f"  ✅ 上传成功 - 耗时: {result['upload_time']:.2f}s")
                print(f"  📋 批次ID: {result.get('batch_id', 'N/A')}")
            else:
                print(f"  ❌ 上传失败: {result.get('error', 'Unknown error')}")
            
            # 获取当前状态
            current_status = await self.get_concurrent_status()
            if current_status.get("success") and not current_status.get("mock"):
                concurrent_info = current_status.get("concurrent_info", {})
                print(f"  📊 当前并发: {concurrent_info.get('current_concurrent_tasks', 'N/A')}/{concurrent_info.get('max_concurrent_tasks', 'N/A')}")
            elif current_status.get("mock"):
                print(f"  📊 监控API不可用，跳过状态检查")
            
            # 等待间隔
            if i < len(file_paths) - 1:  # 最后一个文件不需要等待
                print(f"  ⏳ 等待 {delay_between_uploads} 秒...")
                await asyncio.sleep(delay_between_uploads)
        
        # 等待一段时间让任务处理
        print(f"\n⏳ 等待 10 秒让任务开始处理...")
        await asyncio.sleep(10)
        
        # 获取最终状态
        print("\n📊 获取最终状态...")
        final_status = await self.get_concurrent_status()
        final_queue = await self.get_queue_status()
        
        # 生成测试报告
        self.generate_report(upload_results, initial_status, final_status, initial_queue, final_queue)
        
        # 注意：不清理测试文件，因为这些是实际的测试文档
        print("\n📝 注意：测试使用的是实际文档，不进行文件清理")
        
        return upload_results
    
    def generate_report(self, upload_results: List[Dict], initial_status: Dict, 
                       final_status: Dict, initial_queue: Dict, final_queue: Dict):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📋 连续上传测试报告")
        print("="*60)
        
        # 上传统计
        successful_uploads = sum(1 for r in upload_results if r["success"])
        failed_uploads = len(upload_results) - successful_uploads
        total_time = sum(r["upload_time"] for r in upload_results)
        avg_time = total_time / len(upload_results) if upload_results else 0
        
        print(f"\n📤 上传统计:")
        print(f"  总文件数: {len(upload_results)}")
        print(f"  成功上传: {successful_uploads}")
        print(f"  失败上传: {failed_uploads}")
        print(f"  成功率: {(successful_uploads/len(upload_results)*100):.1f}%")
        print(f"  总耗时: {total_time:.2f}s")
        print(f"  平均耗时: {avg_time:.2f}s")
        
        # 并发状态对比
        print(f"\n📊 并发状态对比:")
        initial_concurrent = initial_status.get('concurrent_info', {}).get('current_concurrent_tasks', 0)
        final_concurrent = final_status.get('concurrent_info', {}).get('current_concurrent_tasks', 0)
        print(f"  初始并发任务: {initial_concurrent}")
        print(f"  最终并发任务: {final_concurrent}")
        print(f"  并发变化: {final_concurrent - initial_concurrent:+d}")
        
        # 队列状态对比
        print(f"\n📋 队列状态对比:")
        initial_queue_stats = initial_queue.get('queue_stats', {})
        final_queue_stats = final_queue.get('queue_stats', {})
        
        for key in ['pending', 'processing', 'completed', 'failed']:
            initial_val = initial_queue_stats.get(key, 0)
            final_val = final_queue_stats.get(key, 0)
            change = final_val - initial_val
            print(f"  {key.capitalize()}: {initial_val} → {final_val} ({change:+d})")
        
        # 测试结论
        print(f"\n🎯 测试结论:")
        if failed_uploads == 0:
            print("  ✅ 所有文件上传成功，连续上传功能正常")
        else:
            print(f"  ⚠️  有 {failed_uploads} 个文件上传失败，需要检查")
        
        if final_concurrent > initial_concurrent:
            print("  ✅ 任务正在后台处理，队列系统工作正常")
        else:
            print("  ⚠️  没有检测到新的后台任务，可能存在问题")
    
    def cleanup_test_files(self, file_paths: List[str]):
        """清理测试文件"""
        print(f"\n🧹 清理测试文件...")
        for file_path in file_paths:
            try:
                os.remove(file_path)
                print(f"  ✅ 删除: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"  ❌ 删除失败: {os.path.basename(file_path)} - {e}")
        
        # 尝试删除测试目录（如果为空）
        try:
            os.rmdir(TEST_FILES_DIR)
            print(f"  ✅ 删除测试目录: {TEST_FILES_DIR}")
        except OSError:
            pass  # 目录不为空或不存在


async def main():
    """主函数"""
    print("🧪 连续上传功能测试")
    print("="*40)
    
    # 检查服务器是否可用
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/") as response:
                if response.status != 200:
                    print(f"❌ 服务器不可用: {BASE_URL}")
                    sys.exit(1)
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        sys.exit(1)
    
    print(f"✅ 服务器连接正常: {BASE_URL}")
    
    # 运行测试
    async with ContinuousUploadTester() as tester:
        await tester.test_continuous_upload(
            file_count=5,  # 测试5个文件
            delay_between_uploads=2.0  # 每次上传间隔2秒
        )
    
    print("\n🎉 连续上传测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
