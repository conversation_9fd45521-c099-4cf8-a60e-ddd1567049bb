#!/usr/bin/env python3
"""
测试Dify向量化流程
"""

import asyncio
from src.core.dependencies import get_dify_service
from src.config.settings import dify_settings

async def test_dify_vectorization():
    """测试Dify向量化流程"""
    
    print("🚀 开始测试Dify向量化流程...")
    
    try:
        # 获取Dify服务
        dify_service = get_dify_service()
        print("✅ Dify服务获取成功")
        
        # 测试Markdown内容
        test_markdown = """
# 测试文档

这是一个测试文档，用于验证Dify向量化功能。

## 主要内容

- 文档解析功能
- 图片处理功能  
- 向量化存储功能

## 技术栈

- Python
- FastAPI
- Dify
- 阿里云文档解析

## 总结

这个测试文档包含了基本的Markdown格式，用于验证Dify知识库的文档创建和向量化功能。
        """
        
        print(f"📄 测试内容长度: {len(test_markdown)} 字符")
        
        # 1. 确保知识库存在
        print("🔍 确保知识库存在...")
        knowledge_base_name = dify_settings.DIFY_DOCUMENT_KNOWLEDGE_BASE
        kb_result = await dify_service.ensure_knowledge_base_exists(knowledge_base_name)
        
        if not kb_result["success"]:
            print(f"❌ 创建知识库失败: {kb_result['error']}")
            return False
        
        dataset_id = kb_result["dataset_id"]
        print(f"✅ 知识库ID: {dataset_id}")
        
        # 2. 创建文档
        print("📝 创建文档...")
        submit_result = await dify_service.create_document_by_text(
            dataset_id=dataset_id,
            name="测试文档_Dify向量化测试.md",
            text=test_markdown
        )
        
        if not submit_result["success"]:
            print(f"❌ 文档创建失败: {submit_result['error']}")
            return False
        
        document_id = submit_result.get("document_id")
        batch_id = submit_result.get("batch_id")
        print(f"✅ 文档创建成功")
        print(f"📄 文档ID: {document_id}")
        print(f"📦 批次ID: {batch_id}")
        
        # 3. 检查向量化状态
        if batch_id:
            print("⏳ 检查向量化状态...")
            max_attempts = 30  # 最多检查30次
            attempt = 0
            
            while attempt < max_attempts:
                attempt += 1
                print(f"  尝试 {attempt}/{max_attempts}...")
                
                status_result = await dify_service.check_document_indexing_status(
                    dataset_id, batch_id
                )
                
                if status_result["success"]:
                    indexing_status = status_result.get("indexing_status", "processing")
                    print(f"  当前状态: {indexing_status}")
                    
                    if indexing_status == "completed":
                        print(f"✅ 向量化完成!")
                        return True
                    elif indexing_status == "error":
                        print(f"❌ 向量化失败")
                        return False
                else:
                    print(f"❌ 状态检查失败: {status_result.get('error', '未知错误')}")
                
                # 等待10秒后重试
                await asyncio.sleep(10)
            
            print(f"⏰ 向量化检查超时，但可能仍在处理中")
            return True
        else:
            print(f"⚠️ 没有获取到batch_id，无法检查向量化状态")
            return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_dify_vectorization())
    
    if success:
        print("\n🎉 Dify向量化测试完成!")
    else:
        print("\n💥 Dify向量化测试失败!")
