#!/usr/bin/env python3
"""
多文件同时上传测试脚本
Test Multi-File Simultaneous Upload

验证同时上传多个文件的功能，确保不会出现阻塞问题
"""

import asyncio
import aiohttp
import json
import time
import os
import sys
from pathlib import Path
from typing import List, Dict, Any

# 测试配置
BASE_URL = "http://localhost:8000"
UPLOAD_ENDPOINT = f"{BASE_URL}/api/v1/documents/upload"
STATUS_ENDPOINT = f"{BASE_URL}/api/v1/documents/concurrent/status"
QUEUE_STATUS_ENDPOINT = f"{BASE_URL}/api/v1/documents/queue/status"

# 测试文件配置
TEST_FILES_DIR = "src/docs/测试文档"


class MultiFileUploadTester:
    """多文件同时上传测试器"""
    
    def __init__(self):
        self.session = None
        self.test_results = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def get_test_files(self, count: int = 5) -> List[str]:
        """获取测试文件"""
        print(f"📁 获取 {count} 个测试文件...")

        # 检查测试文件目录是否存在
        if not os.path.exists(TEST_FILES_DIR):
            print(f"❌ 测试文件目录不存在: {TEST_FILES_DIR}")
            return []

        # 获取目录中的所有PDF文件
        all_files = []
        for filename in os.listdir(TEST_FILES_DIR):
            if filename.lower().endswith('.pdf'):
                filepath = os.path.join(TEST_FILES_DIR, filename)
                all_files.append(filepath)

        if not all_files:
            print(f"❌ 测试文件目录中没有PDF文件: {TEST_FILES_DIR}")
            return []

        # 选择指定数量的文件（如果文件数量不足，就使用所有文件）
        selected_files = all_files[:count] if len(all_files) >= count else all_files

        print(f"  📄 找到 {len(all_files)} 个PDF文件，选择 {len(selected_files)} 个进行测试:")
        for filepath in selected_files:
            filename = os.path.basename(filepath)
            file_size = os.path.getsize(filepath) / 1024  # KB
            print(f"    ✅ {filename} ({file_size:.1f} KB)")

        return selected_files
    
    async def upload_multiple_files(self, file_paths: List[str], batch_name: str = None) -> Dict[str, Any]:
        """同时上传多个文件"""
        print(f"\n📤 同时上传 {len(file_paths)} 个文件...")
        
        try:
            data = aiohttp.FormData()
            
            # 添加所有文件
            for file_path in file_paths:
                filename = os.path.basename(file_path)
                with open(file_path, 'rb') as f:
                    file_content = f.read()
                    data.add_field('files', file_content, filename=filename, content_type='application/pdf')
                print(f"  📄 添加文件: {filename}")
            
            # 添加批次名称
            if batch_name:
                data.add_field('batch_name', batch_name)
            
            start_time = time.time()
            async with self.session.post(UPLOAD_ENDPOINT, data=data) as response:
                end_time = time.time()
                
                result = {
                    "file_count": len(file_paths),
                    "status_code": response.status,
                    "upload_time": end_time - start_time,
                    "success": response.status == 200
                }
                
                if response.status == 200:
                    response_data = await response.json()
                    result["response"] = response_data
                    result["batch_id"] = response_data.get("batch_id")
                    result["uploaded_files"] = response_data.get("uploaded_files", [])
                else:
                    result["error"] = await response.text()
                
                return result
                
        except Exception as e:
            return {
                "file_count": len(file_paths),
                "success": False,
                "error": str(e),
                "upload_time": 0
            }
    
    async def get_concurrent_status(self) -> Dict[str, Any]:
        """获取并发状态"""
        try:
            async with self.session.get(STATUS_ENDPOINT) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    # 暂时跳过监控API错误，返回模拟数据
                    return {"success": False, "error": "监控API暂时不可用", "mock": True}
        except Exception as e:
            return {"success": False, "error": str(e), "mock": True}

    async def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        try:
            async with self.session.get(QUEUE_STATUS_ENDPOINT) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    # 暂时跳过监控API错误，返回模拟数据
                    return {"success": False, "error": "队列监控API暂时不可用", "mock": True}
        except Exception as e:
            return {"success": False, "error": str(e), "mock": True}
    
    async def test_multi_file_upload(self, file_count: int = 5):
        """测试多文件同时上传功能"""
        print(f"\n🚀 开始多文件同时上传测试 - {file_count} 个文件")
        
        # 获取测试文件
        file_paths = self.get_test_files(file_count)
        if not file_paths:
            print("❌ 没有可用的测试文件，测试终止")
            return {}
        
        # 获取初始状态
        print("\n📊 获取初始状态...")
        initial_status = await self.get_concurrent_status()
        initial_queue = await self.get_queue_status()
        
        if initial_status.get("success") and not initial_status.get("mock"):
            concurrent_info = initial_status.get("concurrent_info", {})
            print(f"初始并发任务数: {concurrent_info.get('current_concurrent_tasks', 'N/A')}")
            print(f"最大并发限制: {concurrent_info.get('max_concurrent_tasks', 'N/A')}")
            print(f"可用槽位: {concurrent_info.get('available_slots', 'N/A')}")
        elif initial_status.get("mock"):
            print("⚠️  监控API不可用，跳过并发状态检查")

        if initial_queue.get("success") and not initial_queue.get("mock"):
            queue_stats = initial_queue.get('queue_stats', {})
            print(f"初始队列状态: {queue_stats}")
        elif initial_queue.get("mock"):
            print("⚠️  队列监控API不可用，跳过队列状态检查")
        
        # 同时上传所有文件
        batch_name = f"多文件上传测试_{int(time.time())}"
        upload_result = await self.upload_multiple_files(file_paths, batch_name)
        
        # 显示上传结果
        print(f"\n📋 上传结果:")
        if upload_result["success"]:
            print(f"  ✅ 上传成功 - 耗时: {upload_result['upload_time']:.2f}s")
            print(f"  📋 批次ID: {upload_result.get('batch_id', 'N/A')}")
            print(f"  📄 成功文件数: {len(upload_result.get('uploaded_files', []))}")
            
            # 显示每个文件的状态
            for file_info in upload_result.get('uploaded_files', []):
                print(f"    - {file_info.get('filename', 'N/A')}: {file_info.get('status', 'N/A')}")
        else:
            print(f"  ❌ 上传失败: {upload_result.get('error', 'Unknown error')}")
        
        # 等待一段时间让任务开始处理
        print(f"\n⏳ 等待 10 秒让任务开始处理...")
        await asyncio.sleep(10)
        
        # 获取处理后状态
        print("\n📊 获取处理后状态...")
        processing_status = await self.get_concurrent_status()
        processing_queue = await self.get_queue_status()
        
        if processing_status.get("success"):
            concurrent_info = processing_status.get("concurrent_info", {})
            print(f"处理中并发任务数: {concurrent_info.get('current_concurrent_tasks', 'N/A')}")
            print(f"并发利用率: {concurrent_info.get('concurrent_utilization', 'N/A')}")
        
        if processing_queue.get("success"):
            queue_stats = processing_queue.get('queue_stats', {})
            print(f"处理中队列状态: {queue_stats}")
        
        # 等待更长时间观察处理进度
        print(f"\n⏳ 等待 30 秒观察处理进度...")
        await asyncio.sleep(30)
        
        # 获取最终状态
        print("\n📊 获取最终状态...")
        final_status = await self.get_concurrent_status()
        final_queue = await self.get_queue_status()
        
        # 生成测试报告
        self.generate_report(upload_result, initial_status, processing_status, final_status, 
                           initial_queue, processing_queue, final_queue)
        
        # 注意：不清理测试文件，因为这些是实际的测试文档
        print("\n📝 注意：测试使用的是实际文档，不进行文件清理")
        
        return upload_result
    
    def generate_report(self, upload_result: Dict, initial_status: Dict, processing_status: Dict,
                       final_status: Dict, initial_queue: Dict, processing_queue: Dict, final_queue: Dict):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📋 多文件同时上传测试报告")
        print("="*60)
        
        # 上传统计
        print(f"\n📤 上传统计:")
        print(f"  文件数量: {upload_result['file_count']}")
        print(f"  上传状态: {'成功' if upload_result['success'] else '失败'}")
        print(f"  上传耗时: {upload_result['upload_time']:.2f}s")
        
        if upload_result["success"]:
            uploaded_files = upload_result.get('uploaded_files', [])
            print(f"  成功文件: {len(uploaded_files)}")
            print(f"  平均每文件耗时: {upload_result['upload_time']/len(uploaded_files):.2f}s")
        
        # 并发状态变化
        print(f"\n📊 并发状态变化:")
        
        def get_concurrent_count(status):
            return status.get('concurrent_info', {}).get('current_concurrent_tasks', 0) if status.get('success') else 0
        
        initial_concurrent = get_concurrent_count(initial_status)
        processing_concurrent = get_concurrent_count(processing_status)
        final_concurrent = get_concurrent_count(final_status)
        
        print(f"  初始 → 处理中 → 最终: {initial_concurrent} → {processing_concurrent} → {final_concurrent}")
        print(f"  最大并发增量: {max(processing_concurrent, final_concurrent) - initial_concurrent}")
        
        # 队列状态变化
        print(f"\n📋 队列状态变化:")
        
        def get_queue_stats(queue_status):
            return queue_status.get('queue_stats', {}) if queue_status.get('success') else {}
        
        initial_queue_stats = get_queue_stats(initial_queue)
        processing_queue_stats = get_queue_stats(processing_queue)
        final_queue_stats = get_queue_stats(final_queue)
        
        for key in ['pending', 'processing', 'completed', 'failed']:
            initial_val = initial_queue_stats.get(key, 0)
            processing_val = processing_queue_stats.get(key, 0)
            final_val = final_queue_stats.get(key, 0)
            print(f"  {key.capitalize()}: {initial_val} → {processing_val} → {final_val}")
        
        # 测试结论
        print(f"\n🎯 测试结论:")
        
        if upload_result["success"]:
            print("  ✅ 多文件同时上传成功")
        else:
            print("  ❌ 多文件同时上传失败")
        
        if processing_concurrent > initial_concurrent:
            print("  ✅ 任务成功进入处理队列")
        else:
            print("  ⚠️  未检测到新的处理任务")
        
        total_processed = final_queue_stats.get('completed', 0) + final_queue_stats.get('failed', 0)
        initial_processed = initial_queue_stats.get('completed', 0) + initial_queue_stats.get('failed', 0)
        new_processed = total_processed - initial_processed
        
        if new_processed > 0:
            print(f"  ✅ 有 {new_processed} 个任务已完成处理")
        else:
            print("  ⏳ 任务仍在处理中或处理较慢")
        
        # 性能评估
        if upload_result["success"] and upload_result["upload_time"] < 10:
            print("  ✅ 上传性能良好（< 10秒）")
        elif upload_result["success"]:
            print("  ⚠️  上传性能一般（≥ 10秒）")
    
    def cleanup_test_files(self, file_paths: List[str]):
        """清理测试文件"""
        print(f"\n🧹 清理测试文件...")
        for file_path in file_paths:
            try:
                os.remove(file_path)
                print(f"  ✅ 删除: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"  ❌ 删除失败: {os.path.basename(file_path)} - {e}")
        
        # 尝试删除测试目录（如果为空）
        try:
            os.rmdir(TEST_FILES_DIR)
            print(f"  ✅ 删除测试目录: {TEST_FILES_DIR}")
        except OSError:
            pass  # 目录不为空或不存在


async def main():
    """主函数"""
    print("🧪 多文件同时上传功能测试")
    print("="*40)
    
    # 检查服务器是否可用
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/") as response:
                if response.status != 200:
                    print(f"❌ 服务器不可用: {BASE_URL}")
                    sys.exit(1)
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        sys.exit(1)
    
    print(f"✅ 服务器连接正常: {BASE_URL}")
    
    # 运行测试
    async with MultiFileUploadTester() as tester:
        await tester.test_multi_file_upload(file_count=5)  # 测试5个文件同时上传
    
    print("\n🎉 多文件同时上传测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
